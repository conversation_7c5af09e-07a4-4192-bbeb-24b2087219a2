# PDF导出和打印功能使用指南

## 概述

本项目已升级PDF导出和打印功能，使用新的React组件库来解决oklch颜色函数兼容性问题，并提供更好的用户体验。

## 主要改进

### 1. 解决oklch颜色兼容性问题
- **问题**: 原来使用的 `html2canvas` 在处理现代CSS颜色函数（如oklch）时会出错
- **解决方案**: 使用 `html-to-image` 库替代 `html2canvas`
- **优势**: 更好的颜色兼容性，支持现代CSS特性

### 2. React组件化设计
- **PrintProvider**: 提供打印功能的React组件
- **ExportPrintActions**: 集成PDF导出和打印功能的组合组件
- **ExportButton**: 独立的PDF导出按钮组件

## 安装的依赖

```bash
npm install html-to-image react-to-print
```

## 组件使用方法

### 1. ExportPrintActions 组件

这是最常用的组件，集成了PDF导出和打印功能：

```tsx
import { ExportPrintActions } from '@/components/ExportPrintActions';

<ExportPrintActions
  selector=".content-to-export"  // 要导出的内容选择器
  filename="文档名称"             // PDF文件名
  title="文档标题"               // PDF标题
  className="container-class"    // 容器样式类
  showPrint={true}              // 是否显示打印按钮
  showPDF={true}                // 是否显示PDF导出按钮
  onBeforePrint={() => {}}      // 打印前回调
  onAfterPrint={() => {}}       // 打印后回调
  onBeforeExport={() => {}}     // 导出前回调
  onAfterExport={() => {}}      // 导出后回调
>
  <div className="content-to-export">
    {/* 要导出/打印的内容 */}
  </div>
</ExportPrintActions>
```

### 2. PrintProvider 组件

提供独立的打印功能：

```tsx
import { PrintProvider } from '@/components/PrintProvider';

<PrintProvider
  documentTitle="打印文档标题"
  onBeforePrint={() => {}}
  onAfterPrint={() => {}}
>
  <div>
    {/* 要打印的内容 */}
  </div>
</PrintProvider>
```

### 3. ExportButton 组件

独立的PDF导出按钮：

```tsx
import { ExportButton } from '@/components/ExportPrintActions';

<ExportButton
  selector=".content-to-export"
  filename="文档名称"
  title="文档标题"
  onBeforeExport={() => {}}
  onAfterExport={() => {}}
>
  自定义按钮文本
</ExportButton>
```

### 4. 使用自定义Hook

```tsx
import { usePrint } from '@/components/PrintProvider';

function MyComponent() {
  const { componentRef, handlePrint } = usePrint({
    documentTitle: '自定义文档',
    onBeforePrint: () => console.log('开始打印'),
    onAfterPrint: () => console.log('打印完成')
  });

  return (
    <div>
      <button onClick={handlePrint}>打印</button>
      <div ref={componentRef}>
        {/* 要打印的内容 */}
      </div>
    </div>
  );
}
```

## 样式控制

### 打印样式

组件会自动应用以下打印样式：

```css
@media print {
  /* 隐藏不需要打印的元素 */
  .no-print,
  button,
  .print-button,
  nav,
  header,
  footer {
    display: none !important;
  }
  
  /* 表格样式优化 */
  table {
    border-collapse: collapse !important;
    border: 1px solid #000 !important;
  }
  
  /* 颜色标准化 */
  * {
    color: black !important;
    background: white !important;
  }
}
```

### PDF导出样式

PDF导出时会自动应用临时样式来确保兼容性：

- 移除所有可能包含oklch的颜色属性
- 标准化字体和颜色
- 优化表格和布局样式
- 隐藏不需要的交互元素

## 最佳实践

### 1. 内容标记

为要导出/打印的内容添加适当的CSS类：

```html
<div className="pdf-export-area print-content-area">
  <!-- 主要内容 -->
</div>

<div className="no-print">
  <!-- 不需要打印的按钮等 -->
</div>
```

### 2. 登录验证

在导出PDF前检查用户登录状态：

```tsx
<ExportPrintActions
  onBeforeExport={() => {
    if (!userService.isLoggedIn()) {
      alert("请先登录后再导出PDF");
      window.location.href = "/login";
      return false;
    }
    return true;
  }}
  // ... 其他属性
>
```

### 3. 错误处理

组件内置了错误处理，但你也可以添加自定义处理：

```tsx
<ExportPrintActions
  onAfterExport={(success, error) => {
    if (!success) {
      console.error('导出失败:', error);
      // 自定义错误处理
    }
  }}
  // ... 其他属性
>
```

## 迁移指南

### 从旧的PDF导出方式迁移

**旧方式:**
```tsx
import { exportElementToPDF } from '@/lib/pdfExportUtils';

const handleExport = async () => {
  await exportElementToPDF({
    selector: '.content',
    filename: 'document.pdf',
    title: 'Document'
  });
};
```

**新方式:**
```tsx
import { ExportPrintActions } from '@/components/ExportPrintActions';

<ExportPrintActions
  selector=".content"
  filename="document"
  title="Document"
>
  <div className="content">
    {/* 内容 */}
  </div>
</ExportPrintActions>
```

## 测试

访问 `/test-export` 页面可以测试新的导出和打印功能。

## 技术细节

### 使用的库

1. **html-to-image**: 替代html2canvas，解决oklch兼容性问题
2. **react-to-print**: 提供React友好的打印功能
3. **jsPDF**: 生成PDF文件

### 核心文件

- `components/PrintProvider.tsx`: 打印功能组件
- `components/ExportPrintActions.tsx`: 导出和打印集成组件
- `lib/pdfExportUtils.ts`: PDF导出工具函数（已更新）

### 兼容性

- 支持所有现代浏览器
- 解决了oklch、lab等现代CSS颜色函数的兼容性问题
- 支持深色模式和浅色模式
