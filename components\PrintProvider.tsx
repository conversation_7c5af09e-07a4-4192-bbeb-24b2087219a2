'use client';

import React, { useRef, ReactNode } from 'react';
import { useReactToPrint } from 'react-to-print';
import { Printer } from 'lucide-react';

interface PrintProviderProps {
  children: ReactNode;
  trigger?: ReactNode;
  documentTitle?: string;
  onBeforePrint?: () => void;
  onAfterPrint?: () => void;
  className?: string;
  removeAfterPrint?: boolean;
}

/**
 * 打印组件提供者
 * 使用 react-to-print 库实现打印功能
 */
export function PrintProvider({
  children,
  trigger,
  documentTitle = '打印文档',
  onBeforePrint,
  onAfterPrint,
  className = '',
  removeAfterPrint = false
}: PrintProviderProps) {
  const componentRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle,
    onBeforePrint,
    onAfterPrint,
    removeAfterPrint,
    pageStyle: `
      @page {
        size: A4;
        margin: 20mm;
      }
      
      @media print {
        body {
          font-family: 'SimSun', 'Microsoft YaHei', Arial, sans-serif;
          color: black;
          background: white;
        }
        
        * {
          color: black !important;
          background: white !important;
          background-color: white !important;
          box-shadow: none !important;
          text-shadow: none !important;
          filter: none !important;
          backdrop-filter: none !important;
        }
        
        /* 隐藏不需要打印的元素 */
        .no-print,
        button,
        .print-button,
        nav,
        header,
        footer,
        .hidden.md\\:flex,
        .md\\:hidden {
          display: none !important;
        }
        
        /* 表格样式 */
        table {
          border-collapse: collapse !important;
          width: 100% !important;
          border: 1px solid #000 !important;
        }
        
        th, td {
          border: 1px solid #000 !important;
          padding: 8px !important;
          background: white !important;
          color: black !important;
        }
        
        th {
          background: #f0f0f0 !important;
          font-weight: bold !important;
        }
        
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {
          color: black !important;
          background: white !important;
          border-bottom: 1px solid #ccc !important;
          padding-bottom: 5px !important;
          margin-bottom: 10px !important;
          page-break-after: avoid;
        }
        
        /* 链接样式 */
        a {
          color: black !important;
          text-decoration: underline !important;
        }
        
        /* 分页控制 */
        .page-break-before {
          page-break-before: always;
        }
        
        .page-break-after {
          page-break-after: always;
        }
        
        .page-break-avoid {
          page-break-inside: avoid;
        }
        
        /* 移除所有阴影和特效 */
        .shadow,
        .shadow-sm,
        .shadow-md,
        .shadow-lg {
          box-shadow: none !important;
        }
        
        /* 确保颜色兼容性 */
        .text-blue-600,
        .text-blue-500,
        .text-primary {
          color: #000080 !important;
        }
        
        .text-red-600,
        .text-red-500,
        .text-danger {
          color: #800000 !important;
        }
        
        .text-green-600,
        .text-green-500,
        .text-success {
          color: #008000 !important;
        }
        
        .bg-blue-100,
        .bg-green-100,
        .bg-yellow-100,
        .bg-red-100 {
          background: #f0f0f0 !important;
          border: 1px solid #ccc !important;
        }
      }
    `
  });

  const defaultTrigger = (
    <button
      onClick={handlePrint}
      className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
    >
      <Printer className="w-4 h-4" />
      打印
    </button>
  );

  return (
    <div className={className}>
      {/* 打印触发器 */}
      <div className="no-print">
        {trigger ? (
          <div onClick={handlePrint}>
            {trigger}
          </div>
        ) : (
          defaultTrigger
        )}
      </div>
      
      {/* 要打印的内容 */}
      <div ref={componentRef}>
        {children}
      </div>
    </div>
  );
}

/**
 * 打印按钮组件
 */
interface PrintButtonProps {
  onPrint: () => void;
  className?: string;
  children?: ReactNode;
}

export function PrintButton({ onPrint, className = '', children }: PrintButtonProps) {
  return (
    <button
      onClick={onPrint}
      className={`inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors no-print ${className}`}
    >
      <Printer className="w-4 h-4" />
      {children || '打印'}
    </button>
  );
}

/**
 * 自定义打印Hook
 */
export function usePrint(options?: {
  documentTitle?: string;
  onBeforePrint?: () => void;
  onAfterPrint?: () => void;
  removeAfterPrint?: boolean;
}) {
  const componentRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: options?.documentTitle || '打印文档',
    onBeforePrint: options?.onBeforePrint,
    onAfterPrint: options?.onAfterPrint,
    removeAfterPrint: options?.removeAfterPrint || false,
    pageStyle: `
      @page {
        size: A4;
        margin: 20mm;
      }
      
      @media print {
        body {
          font-family: 'SimSun', 'Microsoft YaHei', Arial, sans-serif;
          color: black;
          background: white;
        }
        
        * {
          color: black !important;
          background: white !important;
          background-color: white !important;
          box-shadow: none !important;
          text-shadow: none !important;
          filter: none !important;
          backdrop-filter: none !important;
        }
        
        .no-print,
        button,
        .print-button,
        nav,
        header,
        footer {
          display: none !important;
        }
        
        table {
          border-collapse: collapse !important;
          width: 100% !important;
          border: 1px solid #000 !important;
        }
        
        th, td {
          border: 1px solid #000 !important;
          padding: 8px !important;
          background: white !important;
          color: black !important;
        }
        
        th {
          background: #f0f0f0 !important;
          font-weight: bold !important;
        }
      }
    `
  });

  return {
    componentRef,
    handlePrint
  };
}
