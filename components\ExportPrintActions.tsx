'use client';

import React, { useState, ReactNode } from 'react';
import { Download, Printer, FileText } from 'lucide-react';
import { exportElementToPDF, sanitizeFilename } from '@/lib/pdfExportUtils';
import { usePrint } from './PrintProvider';

interface ExportPrintActionsProps {
  children: ReactNode;
  selector: string;
  filename: string;
  title?: string;
  className?: string;
  showPrint?: boolean;
  showPDF?: boolean;
  onBeforePrint?: () => void;
  onAfterPrint?: () => void;
  onBeforeExport?: () => void;
  onAfterExport?: () => void;
}

/**
 * 导出和打印操作组件
 * 集成了PDF导出和打印功能
 */
export function ExportPrintActions({
  children,
  selector,
  filename,
  title = '文档',
  className = '',
  showPrint = true,
  showPDF = true,
  onBeforePrint,
  onAfterPrint,
  onBeforeExport,
  onAfterExport
}: ExportPrintActionsProps) {
  const [isExporting, setIsExporting] = useState(false);
  const { componentRef, handlePrint } = usePrint({
    documentTitle: title,
    onBeforePrint,
    onAfterPrint
  });

  const handleExportPDF = async () => {
    if (isExporting) return;

    try {
      setIsExporting(true);
      onBeforeExport?.();

      const sanitizedFilename = sanitizeFilename(filename);
      const finalFilename = sanitizedFilename.endsWith('.pdf') 
        ? sanitizedFilename 
        : `${sanitizedFilename}.pdf`;

      await exportElementToPDF({
        selector,
        filename: finalFilename,
        title,
        scale: 2,
        quality: 0.95
      });

      onAfterExport?.();
    } catch (error) {
      console.error('PDF导出失败:', error);
      alert('PDF导出失败，请重试');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className={className}>
      {/* 操作按钮 */}
      <div className="flex gap-2 mb-4 no-print">
        {showPrint && (
          <button
            onClick={handlePrint}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Printer className="w-4 h-4" />
            打印
          </button>
        )}

        {showPDF && (
          <button
            onClick={handleExportPDF}
            disabled={isExporting}
            className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isExporting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                导出中...
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                导出PDF
              </>
            )}
          </button>
        )}
      </div>

      {/* 要打印/导出的内容 */}
      <div ref={componentRef}>
        {children}
      </div>
    </div>
  );
}

/**
 * 简单的导出按钮组件
 */
interface ExportButtonProps {
  selector: string;
  filename: string;
  title?: string;
  className?: string;
  children?: ReactNode;
  onBeforeExport?: () => void;
  onAfterExport?: () => void;
}

export function ExportButton({
  selector,
  filename,
  title = '文档',
  className = '',
  children,
  onBeforeExport,
  onAfterExport
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (isExporting) return;

    try {
      setIsExporting(true);
      onBeforeExport?.();

      const sanitizedFilename = sanitizeFilename(filename);
      const finalFilename = sanitizedFilename.endsWith('.pdf') 
        ? sanitizedFilename 
        : `${sanitizedFilename}.pdf`;

      await exportElementToPDF({
        selector,
        filename: finalFilename,
        title,
        scale: 2,
        quality: 0.95
      });

      onAfterExport?.();
    } catch (error) {
      console.error('PDF导出失败:', error);
      alert('PDF导出失败，请重试');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <button
      onClick={handleExport}
      disabled={isExporting}
      className={`inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
    >
      {isExporting ? (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          导出中...
        </>
      ) : (
        <>
          <Download className="w-4 h-4" />
          {children || '导出PDF'}
        </>
      )}
    </button>
  );
}

/**
 * 简单的打印按钮组件
 */
interface SimplePrintButtonProps {
  targetRef: React.RefObject<HTMLElement>;
  title?: string;
  className?: string;
  children?: ReactNode;
  onBeforePrint?: () => void;
  onAfterPrint?: () => void;
}

export function SimplePrintButton({
  targetRef,
  title = '打印文档',
  className = '',
  children,
  onBeforePrint,
  onAfterPrint
}: SimplePrintButtonProps) {
  const handlePrint = () => {
    if (!targetRef.current) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const content = targetRef.current.innerHTML;
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>${title}</title>
          <style>
            @page {
              size: A4;
              margin: 20mm;
            }
            
            body {
              font-family: 'SimSun', 'Microsoft YaHei', Arial, sans-serif;
              color: black;
              background: white;
              margin: 0;
              padding: 20px;
            }
            
            * {
              color: black !important;
              background: white !important;
              background-color: white !important;
              box-shadow: none !important;
              text-shadow: none !important;
              filter: none !important;
              backdrop-filter: none !important;
            }
            
            .no-print,
            button,
            .print-button,
            nav,
            header,
            footer {
              display: none !important;
            }
            
            table {
              border-collapse: collapse !important;
              width: 100% !important;
              border: 1px solid #000 !important;
            }
            
            th, td {
              border: 1px solid #000 !important;
              padding: 8px !important;
              background: white !important;
              color: black !important;
            }
            
            th {
              background: #f0f0f0 !important;
              font-weight: bold !important;
            }
            
            h1, h2, h3, h4, h5, h6 {
              color: black !important;
              background: white !important;
              border-bottom: 1px solid #ccc !important;
              padding-bottom: 5px !important;
              margin-bottom: 10px !important;
              page-break-after: avoid;
            }
            
            a {
              color: black !important;
              text-decoration: underline !important;
            }
          </style>
        </head>
        <body>
          ${content}
        </body>
      </html>
    `);

    printWindow.document.close();
    
    printWindow.onload = () => {
      onBeforePrint?.();
      printWindow.print();
      onAfterPrint?.();
      printWindow.close();
    };
  };

  return (
    <button
      onClick={handlePrint}
      className={`inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors ${className}`}
    >
      <Printer className="w-4 h-4" />
      {children || '打印'}
    </button>
  );
}
