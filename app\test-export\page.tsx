'use client';

import React from 'react';
import { ExportPrintActions } from '@/components/ExportPrintActions';
import { PrintProvider } from '@/components/PrintProvider';

export default function TestExportPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          PDF导出和打印功能测试
        </h1>

        {/* 测试 ExportPrintActions 组件 */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            ExportPrintActions 组件测试
          </h2>
          
          <ExportPrintActions
            selector=".test-content"
            filename="测试文档"
            title="测试文档标题"
            className="bg-white rounded-lg shadow-lg p-6"
          >
            <div className="test-content">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                这是要导出的内容
              </h3>
              <p className="text-gray-700 mb-4">
                这是一段测试文本，用于验证PDF导出和打印功能是否正常工作。
                新的实现使用了 html-to-image 替代 html2canvas，解决了 oklch 颜色函数的兼容性问题。
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-2">功能特性</h4>
                  <ul className="text-blue-800 text-sm space-y-1">
                    <li>• 解决 oklch 颜色兼容性问题</li>
                    <li>• 使用 html-to-image 库</li>
                    <li>• 集成 react-to-print</li>
                    <li>• 支持精确区域导出</li>
                  </ul>
                </div>
                
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-900 mb-2">技术优势</h4>
                  <ul className="text-green-800 text-sm space-y-1">
                    <li>• 更好的颜色兼容性</li>
                    <li>• React 组件化设计</li>
                    <li>• 易于集成和使用</li>
                    <li>• 支持自定义样式</li>
                  </ul>
                </div>
              </div>
              
              <table className="w-full border-collapse border border-gray-300 mb-4">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 px-4 py-2 text-left">项目</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">状态</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">说明</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">PDF导出</td>
                    <td className="border border-gray-300 px-4 py-2">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                        已完成
                      </span>
                    </td>
                    <td className="border border-gray-300 px-4 py-2">使用 html-to-image + jsPDF</td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">打印功能</td>
                    <td className="border border-gray-300 px-4 py-2">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                        已完成
                      </span>
                    </td>
                    <td className="border border-gray-300 px-4 py-2">使用 react-to-print</td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2">颜色兼容性</td>
                    <td className="border border-gray-300 px-4 py-2">
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                        已修复
                      </span>
                    </td>
                    <td className="border border-gray-300 px-4 py-2">解决 oklch 函数问题</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </ExportPrintActions>
        </div>

        {/* 测试 PrintProvider 组件 */}
        <div className="mb-12">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            PrintProvider 组件测试
          </h2>
          
          <PrintProvider
            documentTitle="PrintProvider测试文档"
            className="bg-white rounded-lg shadow-lg p-6"
          >
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                PrintProvider 测试内容
              </h3>
              <p className="text-gray-700 mb-4">
                这是使用 PrintProvider 组件的测试内容。
                该组件提供了更灵活的打印功能配置。
              </p>
              
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-900 mb-2">注意事项</h4>
                <p className="text-yellow-800 text-sm">
                  打印时会自动隐藏带有 .no-print 类的元素，
                  并应用专门的打印样式以确保最佳的打印效果。
                </p>
              </div>
            </div>
          </PrintProvider>
        </div>

        <div className="bg-gray-100 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            使用说明
          </h3>
          <div className="text-gray-700 text-sm space-y-2">
            <p>1. 点击"打印"按钮测试打印功能</p>
            <p>2. 点击"导出PDF"按钮测试PDF导出功能</p>
            <p>3. 检查导出的PDF是否包含正确的内容和样式</p>
            <p>4. 验证是否解决了 oklch 颜色函数的兼容性问题</p>
          </div>
        </div>
      </div>
    </div>
  );
}
