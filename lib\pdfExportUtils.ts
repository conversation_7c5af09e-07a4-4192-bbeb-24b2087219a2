/**
 * PDF导出工具函数
 * 使用 html-to-image 替代 html2canvas 解决 oklch 颜色函数兼容性问题
 */

export interface PDFExportOptions {
  selector: string;
  filename: string;
  title?: string;
  scale?: number;
  quality?: number;
}

/**
 * 导出指定区域为PDF
 */
export async function exportElementToPDF(options: PDFExportOptions): Promise<void> {
  const {
    selector,
    filename,
    title = "导出文档",
    scale = 2,
    quality = 0.95
  } = options;

  // 动态导入库
  const [{ default: jsPDF }, { toPng }] = await Promise.all([
    import('jspdf'),
    import('html-to-image')
  ]);

  // 获取要导出的元素
  const element = document.querySelector(selector) as HTMLElement;
  if (!element) {
    throw new Error(`未找到选择器 "${selector}" 对应的元素`);
  }

  // 创建临时样式ID
  const tempStyleId = 'pdf-export-temp-styles-' + Date.now();

  try {
    // 创建临时样式来解决兼容性问题
    const tempStyle = document.createElement('style');
    tempStyle.id = tempStyleId;
    tempStyle.innerHTML = createPDFExportStyles(selector);
    document.head.appendChild(tempStyle);

    // 等待样式应用
    await new Promise(resolve => setTimeout(resolve, 300));

    // 使用 html-to-image 生成图片
    const dataUrl = await toPng(element, {
      quality,
      pixelRatio: scale,
      backgroundColor: '#ffffff',
      width: element.scrollWidth,
      height: element.scrollHeight,
      filter: (node) => {
        return !shouldIgnoreElement(node as Element);
      },
      style: {
        // 确保所有颜色都是标准颜色
        color: 'black',
        backgroundColor: 'white',
        fontFamily: 'SimSun, Microsoft YaHei, Arial, sans-serif'
      }
    });

    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4');

    // 添加标题
    if (title) {
      pdf.setFontSize(16);
      pdf.text(title, 105, 20, { align: 'center' });
    }

    const imgWidth = 210; // A4宽度
    const pageHeight = 295; // A4高度

    // 创建临时图片来获取尺寸
    const img = new Image();
    img.src = dataUrl;

    await new Promise((resolve) => {
      img.onload = resolve;
    });

    const imgHeight = (img.height * imgWidth) / img.width;
    let heightLeft = imgHeight;
    let position = title ? 30 : 0; // 如果有标题，留出空间

    // 添加第一页
    pdf.addImage(dataUrl, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= (pageHeight - position);

    // 如果内容超过一页，添加更多页面
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(dataUrl, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // 下载PDF
    pdf.save(filename);

  } finally {
    // 清理临时样式
    const tempStyleElement = document.getElementById(tempStyleId);
    if (tempStyleElement) {
      tempStyleElement.remove();
    }
  }
}

/**
 * 创建PDF导出专用样式
 */
function createPDFExportStyles(selector: string): string {
  return `
    ${selector} * {
      background: white !important;
      background-color: white !important;
      color: black !important;
      border-color: #000 !important;
      font-family: 'SimSun', 'Microsoft YaHei', Arial, sans-serif !important;
      box-shadow: none !important;
      text-shadow: none !important;
      filter: none !important;
      backdrop-filter: none !important;
      /* 移除所有可能包含 oklch 的属性 */
      accent-color: #000 !important;
      caret-color: #000 !important;
      outline-color: #000 !important;
    }
    
    ${selector} {
      background: white !important;
      background-color: white !important;
      padding: 20px !important;
      margin: 0 !important;
      border: none !important;
      border-radius: 0 !important;
      box-shadow: none !important;
      max-width: none !important;
      width: auto !important;
    }
    
    /* 隐藏不需要的元素 */
    ${selector} .no-print,
    ${selector} button,
    ${selector} .print-button,
    ${selector} .hidden.md\\:flex,
    ${selector} .md\\:hidden,
    ${selector} nav,
    ${selector} header,
    ${selector} footer {
      display: none !important;
    }
    
    /* 表格样式 */
    ${selector} table {
      border-collapse: collapse !important;
      width: 100% !important;
      border: 1px solid #000 !important;
    }
    
    ${selector} th,
    ${selector} td {
      border: 1px solid #000 !important;
      padding: 8px !important;
      background: white !important;
      color: black !important;
    }
    
    ${selector} th {
      background: #f0f0f0 !important;
      font-weight: bold !important;
    }
    
    /* 文本样式 */
    ${selector} h1,
    ${selector} h2,
    ${selector} h3,
    ${selector} h4,
    ${selector} h5,
    ${selector} h6 {
      color: black !important;
      background: white !important;
      border-bottom: 1px solid #ccc !important;
      padding-bottom: 5px !important;
      margin-bottom: 10px !important;
    }
    
    /* 链接样式 */
    ${selector} a {
      color: black !important;
      text-decoration: underline !important;
    }
    
    /* 移除所有阴影和特效 */
    ${selector} .shadow,
    ${selector} .shadow-sm,
    ${selector} .shadow-md,
    ${selector} .shadow-lg {
      box-shadow: none !important;
    }
    
    /* 确保所有颜色都是基础颜色 */
    ${selector} .text-blue-600,
    ${selector} .text-blue-500,
    ${selector} .text-primary {
      color: #000080 !important;
    }
    
    ${selector} .text-red-600,
    ${selector} .text-red-500,
    ${selector} .text-danger {
      color: #800000 !important;
    }
    
    ${selector} .text-green-600,
    ${selector} .text-green-500,
    ${selector} .text-success {
      color: #008000 !important;
    }
    
    ${selector} .bg-blue-100,
    ${selector} .bg-green-100,
    ${selector} .bg-yellow-100,
    ${selector} .bg-red-100 {
      background: #f0f0f0 !important;
      border: 1px solid #ccc !important;
    }
  `;
}

/**
 * 判断是否应该忽略某个元素
 */
function shouldIgnoreElement(element: Element): boolean {
  const classList = element.classList;
  const tagName = element.tagName.toLowerCase();
  
  return (
    classList.contains('no-print') ||
    classList.contains('print-button') ||
    tagName === 'button' ||
    tagName === 'nav' ||
    tagName === 'header' ||
    tagName === 'footer' ||
    (classList.contains('hidden') && classList.contains('md:flex')) ||
    classList.contains('md:hidden')
  );
}



/**
 * 清理文件名，移除特殊字符
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^\w\s\u4e00-\u9fa5.-]/g, '') // 只保留字母、数字、中文、空格、点和横线
    .replace(/\s+/g, '_') // 空格替换为下划线
    .trim();
}
